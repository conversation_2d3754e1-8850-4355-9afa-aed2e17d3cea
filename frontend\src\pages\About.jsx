import { 
  Heart, 
  Users, 
  Target, 
  Award,
  Shield,
  Globe,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  Zap
} from 'lucide-react';

const About = () => {
  const stats = [
    { icon: Users, label: 'Active Donors', value: '2,500+', color: 'text-blue-600' },
    { icon: Heart, label: 'Lives Saved', value: '1,200+', color: 'text-red-500' },
    { icon: Globe, label: 'Cities Covered', value: '15+', color: 'text-green-500' },
    { icon: Award, label: 'Partner Hospitals', value: '25+', color: 'text-purple-500' }
  ];

  const team = [
    {
      name: 'Dr. <PERSON><PERSON>',
      role: 'Founder & CEO',
      bio: 'Former head of emergency medicine with 15+ years of experience in blood bank management.',
      image: '👩‍⚕️'
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      bio: 'Software engineer passionate about using technology to solve healthcare challenges.',
      image: '👨‍💻'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Head of Operations',
      bio: 'Healthcare administrator with expertise in hospital networks and donor coordination.',
      image: '👩‍💼'
    },
    {
      name: 'Dr. <PERSON>',
      role: 'Medical Advisor',
      bio: 'Hematologist and blood transfusion specialist ensuring medical accuracy and safety.',
      image: '👨‍⚕️'
    }
  ];

  const milestones = [
    {
      year: '2023',
      title: 'SomDonate Founded',
      description: 'Started with a vision to revolutionize blood donation in Somalia'
    },
    {
      year: '2023',
      title: 'First Hospital Partnership',
      description: 'Medina Hospital became our first partner, setting the foundation'
    },
    {
      year: '2024',
      title: 'Platform Launch',
      description: 'Officially launched the digital platform connecting donors and hospitals'
    },
    {
      year: '2024',
      title: 'Emergency Network',
      description: 'Introduced 24/7 emergency blood request system'
    }
  ];

  const values = [
    {
      icon: Heart,
      title: 'Life First',
      description: 'Every decision we make prioritizes saving and improving lives.',
      color: 'text-red-600'
    },
    {
      icon: Shield,
      title: 'Safety & Trust',
      description: 'We maintain the highest standards of safety and data protection.',
      color: 'text-blue-600'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Building a strong network of donors, hospitals, and healthcare workers.',
      color: 'text-green-600'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Using cutting-edge technology to make blood donation more efficient.',
      color: 'text-yellow-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-800 text-sm font-medium mb-6">
              <Heart className="h-4 w-4 mr-2" />
              About SomDonate
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
              Connecting Hearts,{' '}
              <span style={{ color: '#4189DD' }}>Saving Lives</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              SomDonate is Somalia's first digital blood bank management system, 
              revolutionizing how donors connect with hospitals to save precious lives 
              across our communities.
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                To create a seamless, efficient, and life-saving blood donation ecosystem 
                that connects willing donors with hospitals in need, ensuring no life is 
                lost due to blood shortage.
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                We believe that technology can bridge the gap between those who want to help 
                and those who need help, making blood donation more accessible, transparent, 
                and impactful for everyone involved.
              </p>
              <div className="flex items-center space-x-4">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Target className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Our Goal</h3>
                  <p className="text-gray-600">Zero preventable deaths due to blood shortage</p>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-100 to-red-100 rounded-2xl p-8 text-center">
                <div className="text-6xl mb-6">🩸</div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  Every Drop Counts
                </h3>
                <p className="text-gray-600">
                  Since our launch, we've facilitated over 1,200 successful blood donations, 
                  directly contributing to saving lives across Somalia.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Our Impact in Numbers
            </h2>
            <p className="text-xl text-gray-600">
              Real results from our commitment to saving lives
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg text-center transform hover:scale-105 transition-transform duration-200">
                <stat.icon className={`h-12 w-12 ${stat.color} mx-auto mb-4`} />
                <div className="text-3xl font-bold text-gray-800 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do at SomDonate
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className={`inline-flex p-4 rounded-full mb-6 ${value.color.replace('text-', 'bg-').replace('-600', '-100')}`}>
                  <value.icon className={`h-8 w-8 ${value.color}`} />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600">
              Key milestones in our mission to save lives
            </p>
          </div>
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200"></div>
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <div className="bg-white p-6 rounded-xl shadow-lg">
                      <div className="text-blue-600 font-bold text-lg mb-2">{milestone.year}</div>
                      <h3 className="text-xl font-bold text-gray-800 mb-2">{milestone.title}</h3>
                      <p className="text-gray-600">{milestone.description}</p>
                    </div>
                  </div>
                  <div className="relative z-10 flex items-center justify-center w-12 h-12 bg-blue-600 rounded-full">
                    <div className="w-6 h-6 bg-white rounded-full"></div>
                  </div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Passionate healthcare professionals and technologists working together to save lives
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-xl text-center hover:shadow-lg transition-shadow">
                <div className="text-6xl mb-4">{member.image}</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">{member.name}</h3>
                <div className="text-blue-600 font-semibold mb-4">{member.role}</div>
                <p className="text-gray-600 text-sm">{member.bio}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Join Our Life-Saving Mission
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Whether you're a donor, hospital, or healthcare professional, 
            there's a place for you in our community.
          </p>
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <a 
              href="/register" 
              className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
            >
              <Heart className="h-5 w-5" />
              <span>Get Started</span>
            </a>
            <a 
              href="/contact" 
              className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              Contact Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
