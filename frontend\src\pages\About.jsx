import {
  Heart,
  Users,
  Target,
  Award,
  Shield,
  Globe,
  Clock,
  CheckCircle,
  Star,
  ArrowRight,
  Mail,
  Phone,
  MapPin,
  Calendar,
  TrendingUp,
  Zap,
  Droplets,
  Activity,
  Building,
  Stethoscope,
  Plus,
  Eye,
  Lightbulb,
  HandHeart,
  AlertCircle
} from 'lucide-react';

const About = () => {
  const impactStats = [
    {
      icon: Heart,
      label: 'Lives Saved',
      value: '1,200+',
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      description: 'Through successful blood donations'
    },
    {
      icon: Users,
      label: 'Active Donors',
      value: '2,500+',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Registered and verified donors'
    },
    {
      icon: Building,
      label: 'Partner Hospitals',
      value: '25+',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Verified healthcare facilities'
    },
    {
      icon: Globe,
      label: 'Cities Reached',
      value: '15+',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Across Somalia'
    }
  ];

  const whyWeExist = [
    {
      icon: Heart,
      title: 'Save Lives Daily',
      description: 'Every 2 seconds, someone in Somalia needs blood. We bridge the gap between those who can give and those who desperately need.',
      color: 'text-red-600',
      bgColor: 'bg-red-50'
    },
    {
      icon: Zap,
      title: 'Emergency Response',
      description: 'Critical situations require immediate action. Our platform connects donors with hospitals in minutes, not hours.',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    },
    {
      icon: Shield,
      title: 'Safe & Reliable',
      description: 'Every donor is verified, every hospital is authenticated, and every donation is tracked for maximum safety.',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    }
  ];

  const ourStory = {
    problem: "In Somalia, finding blood donors during emergencies was a life-or-death challenge. Hospitals struggled to locate compatible donors quickly, and willing donors didn't know where they were needed most.",
    solution: "SomDonate was born from this urgent need - a digital platform that instantly connects blood donors with hospitals, making the difference between life and death.",
    impact: "Today, we're proud to be Somalia's first comprehensive blood bank management system, saving lives every single day."
  };

  const coreValues = [
    {
      icon: Eye,
      title: 'Our Vision',
      description: 'A Somalia where no life is lost due to blood shortage - where every person in need has access to life-saving blood within minutes.',
      gradient: 'from-blue-500 to-blue-600'
    },
    {
      icon: Target,
      title: 'Our Mission',
      description: 'To revolutionize blood donation in Somalia through technology, creating the fastest, safest, and most reliable blood donation network.',
      gradient: 'from-red-500 to-red-600'
    },
    {
      icon: HandHeart,
      title: 'Our Promise',
      description: 'Every donor matters, every request is urgent, and every life saved makes our mission worthwhile. We never stop until help arrives.',
      gradient: 'from-green-500 to-green-600'
    }
  ];

  const milestones = [
    {
      year: '2023',
      title: 'SomDonate Founded',
      description: 'Started with a vision to revolutionize blood donation in Somalia'
    },
    {
      year: '2023',
      title: 'First Hospital Partnership',
      description: 'Medina Hospital became our first partner, setting the foundation'
    },
    {
      year: '2024',
      title: 'Platform Launch',
      description: 'Officially launched the digital platform connecting donors and hospitals'
    },
    {
      year: '2024',
      title: 'Emergency Network',
      description: 'Introduced 24/7 emergency blood request system'
    }
  ];

  const values = [
    {
      icon: Heart,
      title: 'Life First',
      description: 'Every decision we make prioritizes saving and improving lives.',
      color: 'text-red-600'
    },
    {
      icon: Shield,
      title: 'Safety & Trust',
      description: 'We maintain the highest standards of safety and data protection.',
      color: 'text-blue-600'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Building a strong network of donors, hospitals, and healthcare workers.',
      color: 'text-green-600'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'Using cutting-edge technology to make blood donation more efficient.',
      color: 'text-yellow-600'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-red-50 via-white to-blue-50 py-20 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-red-100 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-100 rounded-full opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-red-50 to-blue-50 rounded-full opacity-30"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center space-y-8">
            <div className="space-y-6">
              <div className="inline-flex items-center px-6 py-3 bg-white rounded-full shadow-lg text-gray-800 text-sm font-medium">
                <Heart className="h-5 w-5 mr-2 text-red-500 animate-pulse" />
                About SomDonate
              </div>

              <h1 className="text-5xl md:text-7xl font-bold text-gray-800 leading-tight">
                Every Drop{' '}
                <span className="bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
                  Saves Lives
                </span>
              </h1>

              <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                We're not just a platform - we're a <strong>life-saving network</strong> connecting
                generous hearts with urgent needs across Somalia.
              </p>
            </div>

            {/* Quick Impact */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {impactStats.map((stat, index) => (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-lg transform hover:scale-105 transition-all duration-300">
                  <div className={`${stat.bgColor} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <stat.icon className={`h-8 w-8 ${stat.color}`} />
                  </div>
                  <div className="text-3xl font-bold text-gray-800 mb-1">{stat.value}</div>
                  <div className="text-sm font-medium text-gray-600 mb-1">{stat.label}</div>
                  <div className="text-xs text-gray-500">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Our Story
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Born from urgency, driven by compassion, powered by technology
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Problem */}
            <div className="bg-white rounded-2xl p-8 shadow-lg relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-red-400 to-red-600"></div>
              <div className="text-center mb-6">
                <div className="bg-red-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertCircle className="h-10 w-10 text-red-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">The Problem</h3>
              </div>
              <p className="text-gray-600 leading-relaxed text-center">
                {ourStory.problem}
              </p>
            </div>

            {/* Solution */}
            <div className="bg-white rounded-2xl p-8 shadow-lg relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 to-blue-600"></div>
              <div className="text-center mb-6">
                <div className="bg-blue-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Our Solution</h3>
              </div>
              <p className="text-gray-600 leading-relaxed text-center">
                {ourStory.solution}
              </p>
            </div>

            {/* Impact */}
            <div className="bg-white rounded-2xl p-8 shadow-lg relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-400 to-green-600"></div>
              <div className="text-center mb-6">
                <div className="bg-green-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <TrendingUp className="h-10 w-10 text-green-600" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">Our Impact</h3>
              </div>
              <p className="text-gray-600 leading-relaxed text-center">
                {ourStory.impact}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why We Exist */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Why We Exist
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Every second counts when lives are at stake
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {whyWeExist.map((reason, index) => (
              <div key={index} className="group">
                <div className="bg-white border-2 border-gray-100 rounded-2xl p-8 text-center hover:border-gray-200 transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                  <div className={`${reason.bgColor} w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <reason.icon className={`h-10 w-10 ${reason.color}`} />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">{reason.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{reason.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Vision, Mission, Promise */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              What Drives Us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our commitment to saving lives guides everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {coreValues.map((value, index) => (
              <div key={index} className="group relative">
                <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 border border-gray-100">
                  <div className={`bg-gradient-to-r ${value.gradient} w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <value.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">{value.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{value.description}</p>

                  {/* Decorative element */}
                  <div className={`absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r ${value.gradient} rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Call to Action */}
      <section className="py-20 bg-gradient-to-r from-red-500 via-red-600 to-red-700 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <div className="space-y-8">
            {/* Animated blood drop */}
            <div className="relative">
              <div className="text-8xl mb-6 animate-pulse">🩸</div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-24 h-24 bg-white rounded-full animate-ping opacity-20"></div>
              </div>
            </div>

            <div className="space-y-6">
              <h2 className="text-4xl md:text-6xl font-bold text-white leading-tight">
                Every Second Matters
              </h2>
              <p className="text-xl md:text-2xl text-red-100 max-w-4xl mx-auto leading-relaxed">
                Right now, someone in Somalia needs blood. Your decision to join SomDonate
                could be the difference between life and death.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <a
                href="/register"
                className="bg-white text-red-600 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-200 flex items-center justify-center space-x-3 transform hover:scale-105 shadow-2xl"
              >
                <Heart className="h-6 w-6" />
                <span>Save Lives Today</span>
                <ArrowRight className="h-5 w-5" />
              </a>
              <a
                href="/contact"
                className="bg-transparent border-3 border-white text-white px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-red-600 transition-all duration-200 transform hover:scale-105"
              >
                Learn More
              </a>
            </div>

            {/* Urgency indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto pt-12">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">24/7</div>
                <div className="text-red-100">Emergency Response</div>
              </div>
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">&lt;15min</div>
                <div className="text-red-100">Average Response Time</div>
              </div>
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 text-white">
                <div className="text-3xl font-bold mb-2">100%</div>
                <div className="text-red-100">Verified Network</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Final Message */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <div className="text-6xl">🤝</div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800">
              Together, We Save Lives
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              SomDonate isn't just a platform - it's a community of heroes. Every donor,
              every hospital, every life saved is part of our shared mission to ensure
              no one in Somalia dies from preventable blood shortage.
            </p>
            <div className="bg-gradient-to-r from-blue-50 to-red-50 rounded-2xl p-8">
              <p className="text-lg text-gray-700 italic">
                "The best way to find yourself is to lose yourself in the service of others."
                <br />
                <span className="text-sm font-medium">- Mahatma Gandhi</span>
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
