import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Heart,
  Users,
  Activity,
  Award,
  ArrowRight,
  CheckCircle,
  Clock,
  MapPin,
  Shield,
  Zap,
  Globe,
  Star,
  Phone,
  Mail,
  MessageCircle,
  Target,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

const Home = () => {
  const { isAuthenticated } = useAuth();

  const stats = [
    { icon: Users, label: 'Registered Donors', value: '2,500+', color: 'text-blue-600' },
    { icon: Heart, label: 'Lives Saved', value: '1,200+', color: 'text-red-500' },
    { icon: Activity, label: 'Blood Requests', value: '450+', color: 'text-green-500' },
    { icon: Award, label: 'Partner Hospitals', value: '25+', color: 'text-purple-500' }
  ];

  const features = [
    {
      icon: Clock,
      title: 'Quick Response',
      description: 'Get matched with nearby donors within minutes of posting a request.',
      color: 'text-blue-600'
    },
    {
      icon: MapPin,
      title: 'Location-Based',
      description: 'Find donors and requests in your area for faster blood delivery.',
      color: 'text-green-600'
    },
    {
      icon: CheckCircle,
      title: 'Verified Users',
      description: 'All donors and hospitals are verified for safety and reliability.',
      color: 'text-purple-600'
    },
    {
      icon: Shield,
      title: 'Secure & Safe',
      description: 'Your data is protected with enterprise-grade security measures.',
      color: 'text-red-600'
    },
    {
      icon: Zap,
      title: 'Real-time Alerts',
      description: 'Instant notifications for urgent blood requests and matches.',
      color: 'text-yellow-600'
    },
    {
      icon: Globe,
      title: 'Wide Coverage',
      description: 'Serving communities across Somalia with expanding network.',
      color: 'text-indigo-600'
    }
  ];

  const testimonials = [
    {
      name: 'Dr. Ahmed Hassan',
      role: 'Chief Medical Officer, Medina Hospital',
      content: 'SomDonate has revolutionized how we connect with blood donors. The response time has improved dramatically.',
      rating: 5
    },
    {
      name: 'Fatima Ali',
      role: 'Regular Blood Donor',
      content: 'I love how easy it is to find nearby hospitals that need my blood type. Saving lives has never been simpler.',
      rating: 5
    },
    {
      name: 'Dr. Omar Mohamed',
      role: 'Emergency Department, Banadir Hospital',
      content: 'The verification system gives us confidence in the donors. It\'s a game-changer for emergency situations.',
      rating: 5
    }
  ];

  const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];

  const howItWorks = [
    {
      step: '1',
      title: 'Register',
      description: 'Sign up as a donor or hospital with verified credentials',
      icon: Users
    },
    {
      step: '2',
      title: 'Get Matched',
      description: 'Our system matches donors with nearby hospitals in need',
      icon: Target
    },
    {
      step: '3',
      title: 'Save Lives',
      description: 'Complete the donation and help save precious lives',
      icon: Heart
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-red-50 py-20 overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-red-100 rounded-full opacity-20"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center px-4 py-2 bg-blue-100 rounded-full text-blue-800 text-sm font-medium">
                  <Heart className="h-4 w-4 mr-2" />
                  Saving Lives Together
                </div>
                <h1 className="text-4xl md:text-6xl font-bold text-gray-800 leading-tight">
                  Save Lives with{' '}
                  <span className="blood-drop bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                    🩸 SomDonate
                  </span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  Connect blood donors with hospitals in need across Somalia. Every donation counts,
                  and together we can make a life-saving difference in our community.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                {!isAuthenticated ? (
                  <>
                    <Link
                      to="/register"
                      className="btn-primary flex items-center justify-center space-x-2 transform hover:scale-105 transition-all duration-200"
                    >
                      <Heart className="h-5 w-5" />
                      <span>Become a Donor</span>
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                    <Link
                      to="/login"
                      className="px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105"
                    >
                      <span>Hospital Login</span>
                    </Link>
                  </>
                ) : (
                  <Link
                    to="/dashboard"
                    className="btn-primary flex items-center justify-center space-x-2 transform hover:scale-105 transition-all duration-200"
                  >
                    <Activity className="h-5 w-5" />
                    <span>Go to Dashboard</span>
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                )}
              </div>

              {/* Quick stats */}
              <div className="grid grid-cols-3 gap-4 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">2,500+</div>
                  <div className="text-sm text-gray-600">Active Donors</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">1,200+</div>
                  <div className="text-sm text-gray-600">Lives Saved</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">25+</div>
                  <div className="text-sm text-gray-600">Hospitals</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="text-center">
                  <div className="relative">
                    <Heart className="h-24 w-24 text-red-500 fill-current mx-auto mb-6 blood-drop animate-pulse" />
                    <div className="absolute inset-0 h-24 w-24 mx-auto">
                      <div className="absolute inset-0 bg-red-200 rounded-full animate-ping opacity-20"></div>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    One Donation = Three Lives Saved
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Your single blood donation can be separated into red cells,
                    platelets, and plasma to help multiple patients.
                  </p>
                  <div className="grid grid-cols-4 gap-2">
                    {bloodTypes.map((type) => (
                      <div key={type} className="bg-red-50 text-red-700 text-sm font-semibold py-2 px-3 rounded-lg">
                        {type}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 rounded-lg bg-gray-50 hover:shadow-lg transition-shadow">
                <stat.icon className={`h-12 w-12 ${stat.color} mx-auto mb-4`} />
                <div className="text-3xl font-bold text-gray-800 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              How SomDonate Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Simple steps to save lives and make a difference in your community.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {howItWorks.map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-2xl font-bold text-blue-600">{step.step}</span>
                </div>
                <step.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-800 mb-4">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
                {index < howItWorks.length - 1 && (
                  <ArrowRight className="hidden md:block absolute top-8 -right-4 h-6 w-6 text-gray-400" />
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Why Choose SomDonate?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform makes blood donation simple, safe, and efficient for everyone involved.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className={`inline-flex p-3 rounded-lg mb-6 ${feature.color.replace('text-', 'bg-').replace('-600', '-100')}`}>
                  <feature.icon className={`h-8 w-8 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              What Our Community Says
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real stories from donors and hospitals who are making a difference.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gray-50 p-8 rounded-xl relative">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-6 italic">"{testimonial.content}"</p>
                <div className="flex items-center">
                  <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-bold text-lg">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-800">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Section */}
      <section className="py-16 bg-red-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <div className="inline-flex items-center px-4 py-2 bg-red-100 rounded-full text-red-800 text-sm font-medium mb-6">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Emergency Blood Needed
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                  Every Second Counts in Emergencies
                </h2>
                <p className="text-xl text-gray-600 mb-8">
                  When hospitals face critical blood shortages, our emergency alert system
                  connects them with compatible donors instantly. Be part of the life-saving network.
                </p>
                <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                  <Link
                    to="/register"
                    className="bg-red-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
                  >
                    <Heart className="h-5 w-5" />
                    <span>Join Emergency Network</span>
                  </Link>
                  <Link
                    to="/about"
                    className="border-2 border-red-600 text-red-600 px-6 py-3 rounded-lg font-semibold hover:bg-red-50 transition-colors"
                  >
                    Learn More
                  </Link>
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-red-100 to-red-200 rounded-xl p-8 text-center">
                  <div className="relative">
                    <div className="text-6xl mb-4">🚨</div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-20 h-20 bg-red-200 rounded-full animate-ping opacity-20"></div>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-red-800 mb-4">24/7 Emergency Response</h3>
                  <p className="text-red-700">
                    Our system works around the clock to match urgent requests with available donors.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
          <div className="space-y-8">
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold text-white">
                Ready to Save Lives?
              </h2>
              <p className="text-xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
                Join thousands of donors and hospitals already using SomDonate to make a life-saving difference.
                Your contribution matters more than you know.
              </p>
            </div>

            {!isAuthenticated && (
              <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <Link
                  to="/register"
                  className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-200 flex items-center justify-center space-x-2 transform hover:scale-105 shadow-lg"
                >
                  <Heart className="h-5 w-5" />
                  <span>Register as Donor</span>
                </Link>
                <Link
                  to="/register"
                  className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-200 transform hover:scale-105"
                >
                  Register as Hospital
                </Link>
              </div>
            )}

            {/* Contact info */}
            <div className="pt-8 border-t border-blue-500">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-blue-100">
                <div className="flex items-center justify-center space-x-2">
                  <Phone className="h-5 w-5" />
                  <span>+252 61 234 5678</span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <Mail className="h-5 w-5" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center justify-center space-x-2">
                  <MessageCircle className="h-5 w-5" />
                  <span>24/7 Support Available</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
