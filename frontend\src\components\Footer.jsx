import { Heart, Mail, Phone, MapPin, Clock, Shield, Users } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-red-900 text-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-red-500 p-2 rounded-full animate-pulse">
                <Heart className="h-8 w-8 text-white fill-current" />
              </div>
              <span className="text-2xl font-bold">
                🩸 <span className="bg-gradient-to-r from-red-400 to-red-500 bg-clip-text text-transparent">SomDonate</span>
              </span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md text-lg leading-relaxed">
              Somalia's first digital blood bank management system, connecting generous hearts
              with urgent needs to save precious lives across our communities.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-300">
                <div className="bg-red-500 bg-opacity-20 p-2 rounded-lg">
                  <Phone className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="font-medium">Emergency Hotline</div>
                  <div className="text-sm">+252 61 234 5678</div>
                </div>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <div className="bg-red-500 bg-opacity-20 p-2 rounded-lg">
                  <Mail className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="font-medium">Email Support</div>
                  <div className="text-sm"><EMAIL></div>
                </div>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <div className="bg-red-500 bg-opacity-20 p-2 rounded-lg">
                  <Clock className="h-5 w-5 text-red-400" />
                </div>
                <div>
                  <div className="font-medium">Available 24/7</div>
                  <div className="text-sm">Emergency Response</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-red-400">
              Quick Links
            </h3>
            <ul className="space-y-3">
              <li>
                <a href="/" className="text-gray-300 hover:text-red-400 transition-all duration-200 flex items-center space-x-2 group">
                  <span className="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  <span>Home</span>
                </a>
              </li>
              <li>
                <a href="/about" className="text-gray-300 hover:text-red-400 transition-all duration-200 flex items-center space-x-2 group">
                  <span className="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  <span>About Us</span>
                </a>
              </li>
              <li>
                <a href="/contact" className="text-gray-300 hover:text-red-400 transition-all duration-200 flex items-center space-x-2 group">
                  <span className="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  <span>Contact Us</span>
                </a>
              </li>
              <li>
                <a href="/register" className="text-gray-300 hover:text-red-400 transition-all duration-200 flex items-center space-x-2 group">
                  <span className="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  <span>Become a Donor</span>
                </a>
              </li>
              <li>
                <a href="/blood-requests" className="text-gray-300 hover:text-red-400 transition-all duration-200 flex items-center space-x-2 group">
                  <span className="w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  <span>Find Blood Requests</span>
                </a>
              </li>
            </ul>
          </div>

          {/* Blood Types */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-red-400">
              Blood Types We Need
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map((type) => (
                <div
                  key={type}
                  className="bg-gradient-to-r from-red-500 to-red-600 text-white text-center py-3 px-4 rounded-xl text-sm font-bold hover:from-red-600 hover:to-red-700 transition-all duration-200 transform hover:scale-105 cursor-pointer shadow-lg"
                >
                  {type}
                </div>
              ))}
            </div>

            {/* Emergency Notice */}
            <div className="mt-6 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="h-5 w-5 text-red-400" />
                <span className="text-red-400 font-semibold text-sm">Emergency Priority</span>
              </div>
              <p className="text-gray-300 text-sm">
                O- and AB+ blood types are in critical demand. Your donation can save multiple lives.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="bg-red-500 bg-opacity-20 rounded-2xl p-6">
                <Users className="h-8 w-8 text-red-400 mx-auto mb-3" />
                <div className="text-2xl font-bold text-white mb-1">2,500+</div>
                <div className="text-gray-300 text-sm">Active Donors</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-red-500 bg-opacity-20 rounded-2xl p-6">
                <Heart className="h-8 w-8 text-red-400 mx-auto mb-3" />
                <div className="text-2xl font-bold text-white mb-1">1,200+</div>
                <div className="text-gray-300 text-sm">Lives Saved</div>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-red-500 bg-opacity-20 rounded-2xl p-6">
                <Shield className="h-8 w-8 text-red-400 mx-auto mb-3" />
                <div className="text-2xl font-bold text-white mb-1">25+</div>
                <div className="text-gray-300 text-sm">Partner Hospitals</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-red-500 border-opacity-30 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-300 text-center md:text-left">
              <div className="text-lg font-semibold mb-1">
                © 2024 <span className="text-red-400">SomDonate</span>. All rights reserved.
              </div>
              <div className="text-sm">
                Saving lives, one donation at a time. 🩸
              </div>
            </div>

            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <div className="flex items-center space-x-2 text-gray-300">
                <div className="bg-red-500 bg-opacity-20 p-1 rounded">
                  <MapPin className="h-4 w-4 text-red-400" />
                </div>
                <span className="text-sm">Medina District, Mogadishu, Somalia</span>
              </div>

              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm font-medium">System Online</span>
              </div>
            </div>
          </div>

          {/* Emergency Banner */}
          <div className="mt-6 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
              <span className="text-white font-bold text-sm">EMERGENCY BLOOD NEEDED</span>
              <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
            </div>
            <p className="text-red-100 text-sm">
              Critical shortage of O- blood type. Call <span className="font-bold text-white">+252 61 234 5678</span> to donate now.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
