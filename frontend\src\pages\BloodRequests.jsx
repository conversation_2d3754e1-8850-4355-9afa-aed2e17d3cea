import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Heart, MapPin, Clock, AlertCircle, Plus, Loader2 } from 'lucide-react';
import { requestAPI } from '../services/api';
import { toast } from 'react-hot-toast';

const BloodRequests = () => {
  const { isHospital, isDonor, user } = useAuth();
  const [activeTab, setActiveTab] = useState('all');
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [matchedDonors, setMatchedDonors] = useState([]);

  // Form state for creating new request
  const [newRequest, setNewRequest] = useState({
    bloodType: '',
    units: 1,
    urgency: 'medium',
    location: {
      address: '',
      coordinates: {
        latitude: 0,
        longitude: 0
      }
    },
    notes: ''
  });

  useEffect(() => {
    fetchRequests();
  }, [activeTab]);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      let response;
      
      if (isHospital) {
        response = await requestAPI.getHospitalRequests();
      } else {
        // Get user's location from browser
        const position = await new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        
        response = await requestAPI.getNearbyRequests({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
      }
      
      setRequests(response.data.requests);
    } catch (error) {
      console.error('Error fetching requests:', error);
      toast.error('Failed to fetch blood requests');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRequest = async (e) => {
    e.preventDefault();
    try {
      const requestData = {
        bloodType: newRequest.bloodType,
        units: newRequest.units,
        urgency: newRequest.urgency,
        location: newRequest.location.address,
        description: newRequest.notes,
      };
      const response = await requestAPI.createRequest(requestData);
      toast.success('Blood request created successfully');
      setShowCreateModal(false);
      fetchRequests();
    } catch (error) {
      console.error('Error creating request:', error);
      toast.error('Failed to create blood request');
    }
  };

  const handleFulfillRequest = async (requestId) => {
    try {
      await requestAPI.fulfillRequest(requestId);
      toast.success('Request fulfilled successfully');
      fetchRequests();
    } catch (error) {
      console.error('Error fulfilling request:', error);
      toast.error('Failed to fulfill request');
    }
  };

  const handleViewDetails = async (request) => {
    setSelectedRequest(request);
    try {
      const response = await requestAPI.getMatchedDonors(request._id);
      setMatchedDonors(response.data.matchedDonors);
    } catch (error) {
      console.error('Error fetching matched donors:', error);
      toast.error('Failed to fetch matched donors');
    }
    setShowDetailsModal(true);
  };

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">
              {isHospital ? 'My Blood Requests' : 'Available Blood Requests'}
            </h1>
            <p className="text-gray-600 mt-2">
              {isHospital 
                ? 'Manage your hospital\'s blood requests and track responses'
                : 'Find blood requests in your area and help save lives'
              }
            </p>
          </div>
          {isHospital && (
            <button 
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center space-x-2"
            >
              <Plus className="h-5 w-5" />
              <span>Create Request</span>
            </button>
          )}
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {['all', 'pending', 'fulfilled'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab} Requests
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Requests Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {requests
            .filter(request => activeTab === 'all' || request.status === activeTab)
            .map((request) => (
            <div key={request._id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              {/* Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-red-100 p-2 rounded-full">
                    <Heart className="h-6 w-6 text-red-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">{request.bloodType}</h3>
                    <p className="text-sm text-gray-600">{request.units} units needed</p>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                  {request.urgency} priority
                </span>
              </div>

              {/* Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sm">{request.hospital?.name || 'Unknown Hospital'}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sm">{request.location.address}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Posted {new Date(request.createdAt).toLocaleDateString()}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                {isDonor && request.status === 'pending' && (
                  <button 
                    onClick={() => handleFulfillRequest(request._id)}
                    className="flex-1 btn-primary text-sm"
                  >
                    Respond to Request
                  </button>
                )}
                {isHospital && (
                  <>
                    {request.status === 'pending' && (
                      <button 
                        onClick={() => handleFulfillRequest(request._id)}
                        className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                      >
                        Mark Fulfilled
                      </button>
                    )}
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                      Edit
                    </button>
                  </>
                )}
                <button 
                  onClick={() => handleViewDetails(request)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                >
                  Details
                </button>
              </div>

              {/* Status Badge */}
              {request.status === 'fulfilled' && (
                <div className="mt-4 flex items-center space-x-2 text-green-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Request Fulfilled</span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {requests.filter(request => activeTab === 'all' || request.status === activeTab).length === 0 && (
          <div className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No requests found</h3>
            <p className="text-gray-600">
              {activeTab === 'all' 
                ? 'No blood requests available at the moment.'
                : `No ${activeTab} requests found.`
              }
            </p>
          </div>
        )}

        {/* Create Request Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <h2 className="text-2xl font-bold mb-4">Create Blood Request</h2>
              <form onSubmit={handleCreateRequest}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Blood Type</label>
                    <select
                      value={newRequest.bloodType}
                      onChange={(e) => setNewRequest({...newRequest, bloodType: e.target.value})}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      required
                    >
                      <option value="">Select Blood Type</option>
                      {['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Units Needed</label>
                    <input
                      type="number"
                      min="1"
                      value={newRequest.units}
                      onChange={(e) => setNewRequest({...newRequest, units: parseInt(e.target.value)})}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Urgency</label>
                    <select
                      value={newRequest.urgency}
                      onChange={(e) => setNewRequest({...newRequest, urgency: e.target.value})}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      required
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Location</label>
                    <input
                      type="text"
                      value={newRequest.location.address}
                      onChange={(e) => setNewRequest({
                        ...newRequest,
                        location: {
                          ...newRequest.location,
                          address: e.target.value
                        }
                      })}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <textarea
                      value={newRequest.notes}
                      onChange={(e) => setNewRequest({...newRequest, notes: e.target.value})}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                      rows="3"
                    />
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowCreateModal(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Create Request
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Request Details Modal */}
        {showDetailsModal && selectedRequest && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
              <h2 className="text-2xl font-bold mb-4">Request Details</h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Blood Type</h3>
                    <p className="mt-1">{selectedRequest.bloodType}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Units Needed</h3>
                    <p className="mt-1">{selectedRequest.units}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Urgency</h3>
                    <p className="mt-1 capitalize">{selectedRequest.urgency}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Status</h3>
                    <p className="mt-1 capitalize">{selectedRequest.status}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Location</h3>
                  <p className="mt-1">{selectedRequest.location.address}</p>
                </div>

                {selectedRequest.notes && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Notes</h3>
                    <p className="mt-1">{selectedRequest.notes}</p>
                  </div>
                )}

                {isHospital && matchedDonors.length > 0 && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Matched Donors</h3>
                    <div className="space-y-2">
                      {matchedDonors.map(donor => (
                        <div key={donor._id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div>
                            <p className="font-medium">{donor.name}</p>
                            <p className="text-sm text-gray-500">Blood Type: {donor.bloodType}</p>
                          </div>
                          <button
                            onClick={() => handleFulfillRequest(selectedRequest._id)}
                            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                          >
                            Contact
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BloodRequests;
